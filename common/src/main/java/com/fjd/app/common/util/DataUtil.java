package com.fjd.app.common.util;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;

public class DataUtil {

	private static final char[] HEX = {
		'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'
	};

	/**
	 * 将一个单字节的byte转换成32位的int
	 *
	 * @param b byte
	 * @return convert result
	 */
	public static int unsignedByteToInt(byte b) {
		return (int) b & 0xFF;
	}

	/**
	 * 将一个单字节的Byte转换成十六进制的数
	 *
	 * @param b byte
	 * @return convert result
	 */
	public static String byteToHex(byte b) {
		int i = b & 0xFF;
		return Integer.toHexString(i);
	}

	public static int bytes2Int(byte high, byte low) {
		return low & 0xFF | (high & 0xFF) << 8;
	}

	/**
	 * 将一个4byte的数组转换成32位的int
	 *
	 * @param buf bytes buffer
	 * @param
	 * @return convert result
	 */
	public static long unsigned4BytesToInt(byte[] buf, int pos) {
		int firstByte = 0;
		int secondByte = 0;
		int thirdByte = 0;
		int fourthByte = 0;
		int index = pos;
		firstByte = (0x000000FF & ((int) buf[index]));
		secondByte = (0x000000FF & ((int) buf[index + 1]));
		thirdByte = (0x000000FF & ((int) buf[index + 2]));
		fourthByte = (0x000000FF & ((int) buf[index + 3]));
		index = index + 4;
		return ((long) (firstByte << 24 | secondByte << 16 | thirdByte << 8 | fourthByte))
			& 0xFFFFFFFFL;
	}

	/**
	 * 将16位的short转换成byte数组
	 *
	 * @param s short
	 * @return byte[] 长度为2
	 */
	public static byte[] shortToByteArray(short s) {
		byte[] targets = new byte[2];
		for (int i = 0; i < 2; i++) {
			int offset = (targets.length - 1 - i) * 8;
			targets[i] = (byte) ((s >>> offset) & 0xff);
		}
		return targets;
	}

	/**
	 * 将32位整数转换成长度为4的byte数组
	 *
	 * @param s int
	 * @return byte[]
	 */
	public static byte[] intToByteArray(int s) {
		byte[] targets = new byte[2];
		for (int i = 0; i < 4; i++) {
			int offset = (targets.length - 1 - i) * 8;
			targets[i] = (byte) ((s >>> offset) & 0xff);
		}
		return targets;
	}

	/**
	 * long to byte[]
	 *
	 * @param s long
	 * @return byte[]
	 */
	public static byte[] longToByteArray(long s) {
		long temp = s;
		byte[] b = new byte[8];
		for (int i = 0; i < b.length; i++) {
			b[7 - i] = Long.valueOf(temp & 0xff).byteValue();
			temp = temp >> 8; // 向右移8位
		}
		return b;
	}

	/**
	 * 32位int转byte[]
	 */
	public static byte[] int2byte(int res) {
		byte[] targets = new byte[4];
		targets[3] = (byte) (res & 0xff); // 最低位
		targets[2] = (byte) ((res >> 8) & 0xff); // 次低位
		targets[1] = (byte) ((res >> 16) & 0xff); // 次高位
		targets[0] = (byte) (res >>> 24); // 最高位,无符号右移。
		return targets;
	}

	/**
	 * 16位unsigned int转byte[]
	 */
	public static byte[] int2byte2(int res) {
		byte[] targets = new byte[2];
		targets[0] = (byte) ((res >> 8) & 0xff); // 次低位
		targets[1] = (byte) (res & 0xff); // 最低位
		return targets;
	}

	/**
	 * 16位signed int转byte[]
	 */
	public static byte[] signedInt2byte2(int res) {
		byte[] targets = new byte[2];
		targets[0] = (byte) (res >> 8); // 次低位
		targets[1] = (byte) (res); // 最低位
		return targets;
	}

	/**
	 * 16位int转byte[]
	 */
	public static byte[] int2byte1(int res) {
		byte[] targets = new byte[1];
		targets[0] = (byte) (res & 0xff); // 最低位
		return targets;
	}

	/**
	 * 将长度为32的byte数组转换为16位int
	 *
	 * @param res byte[]
	 * @return int
	 */
	public static int byte2int(byte[] res) {
		return res[3] & 0xFF | (res[2] & 0xFF) << 8 | (res[1] & 0xFF) << 16 | (res[0] & 0xFF) << 24;
	}

	/**
	 * 将长度为4的byte数组转换为float
	 *
	 * @param res byte[]
	 * @return int
	 */
	public static float byte2Float(byte[] res) {
		int result =
			res[3] & 0xFF
				| (res[2] & 0xFF) << 8
				| (res[1] & 0xFF) << 16
				| (res[0] & 0xFF) << 24;
		return (float) result / 100;
	}

	/**
	 * short类型数据，发送方先*100，转为int，转为2个byte发送出来，然后接收方需要先补足为4个byte数组，转成float，但是要*10，getFloat是/1000
	 */
	public static float byte2short(byte[] res) {

		byte[] shortToIntBytes = {0x00, 0x00, res[0], res[1]};
		return getFloat(shortToIntBytes, 0) * 10;
	}

	public static int byte2short2(byte[] res) {
		byte[] shortToIntBytes = {0x00, 0x00, res[0], res[1]};
		return byte2int(shortToIntBytes);
	}

	public static byte[] byteMerger(byte[] bt1, byte[] bt2) {
		byte[] bt3 = new byte[bt1.length + bt2.length];
		System.arraycopy(bt1, 0, bt3, 0, bt1.length);
		System.arraycopy(bt2, 0, bt3, bt1.length, bt2.length);
		return bt3;
	}

	/**
	 * 合并多个byte数组
	 *
	 * @param byteArrays all byte arrays to be merged
	 * @return result
	 */
	public static byte[] mergeByteArrays(byte[]... byteArrays) {
		int length = 0;
		for (byte[] byteArray : byteArrays) {
			length += byteArray.length;
		}
		byte[] result = new byte[length];
		int index = 0;
		for (byte[] byteArray : byteArrays) {
			System.arraycopy(byteArray, 0, result, index, byteArray.length);
			index += byteArray.length;
		}
		return result;
	}

	public static byte[] double2Bytes(double d) {
		//        long value = Double.doubleToRawLongBits(d);
		//        byte[] byteRet = new byte[8];
		//        for (int i = 0; i < 8; i++) {
		//            byteRet[i] = (byte) ((value >> 8 * i) & 0xff);
		//        }
		//
		//        return byteRet;
		long s = (long) (d * (Math.pow(10, 8)));

		return longToByteArray(s);
	}

	public static double bytes2Double(byte[] arr) {
		long value = bytesToLong(arr);
		return (value / (Math.pow(10, 7)));
	}

	// float转换为byte[4]数组
	public static byte[] float2Byte(float f) {
		int intbits = (int) (f * (Math.pow(10, 3)));
		// 将float里面的二进制串解释为int整数
		return int2byte(intbits);
	}

	public static long bytesToLong(byte[] buffer) {
		long values = 0;
		for (int i = 0; i < 8; i++) {
			values <<= 8;
			values |= (buffer[i] & 0xff);
		}
		return values;
	}

	// 从byte数组的index处的连续4个字节获得一个float
	public static float getFloat(byte[] arr, int index) {
		int s = byte2int(arr);
		return (float) (s / (Math.pow(10, 3)));
	}

	// 从byte数组的index处的连续4个字节获得一个int
	public static int getInt(byte[] arr, int index) {
		return (0xff000000 & (arr[index + 0] << 24))
			| (0x00ff0000 & (arr[index + 1] << 16))
			| (0x0000ff00 & (arr[index + 2] << 8))
			| (0x000000ff & arr[index + 3]);
	}

	public static byte[] subBytes(byte[] src, int begin, int count) {
		byte[] bs = new byte[count];
		if (begin + count <= src.length) {
			System.arraycopy(src, begin, bs, 0, count);
		}

		return bs;
	}

	/**
	 * 有符号，int 占 2 个字节
	 */
	public static int convertTwoSignInt(byte b1, byte b2) { // signed
		return (b2 << 8) | (b1 & 0xFF);
	}

	/**
	 * 有符号，int 占 2 个字节
	 */
	public static int convertTwoSignInt(byte[] b) { // signed
		byte b1 = b[1];
		byte b2 = b[0];
		return (b2 << 8) | (b1 & 0xFF);
	}

	/**
	 * 有符号，int 占 2 个字节 LSB
	 */
	public static int convertTwoSignIntLsb(byte[] b) { // signed
		byte b1 = b[0];
		byte b2 = b[1];
		return (b2 << 8) | (b1 & 0xFF);
	}

	/**
	 * 有符号, int 占 4 个字节
	 */
	public static int convertFourSignInt(byte b1, byte b2, byte b3, byte b4) {
		return (b4 << 24) | (b3 & 0xFF) << 16 | (b2 & 0xFF) << 8 | (b1 & 0xFF);
	}

	/**
	 * 有符号, int 占 4 个字节
	 */
	public static int convertFourSignInt(byte[] b) {
		return convertFourSignInt(b[3], b[2], b[1], b[0]);
	}

	/**
	 * 有符号, int 占 4 个字节
	 */
	public static int convertFourSignLSB(byte[] b) {
		return convertFourSignInt(b[0], b[1], b[2], b[3]);
	}

	/**
	 * 无符号，int 占 2 个字节
	 */
	public static int convertTwoUnsignInt(byte b1, byte b2) // unsigned
	{
		return (b2 & 0xFF) << 8 | (b1 & 0xFF);
	}

	/**
	 * 无符号, int 占 4 个字节
	 */
	public static long convertFoutUnsignLong(byte b1, byte b2, byte b3, byte b4) {
		return (long) (b4 & 0xFF) << 24 | (b3 & 0xFF) << 16 | (b2 & 0xFF) << 8 | (b1 & 0xFF);
	}

	/**
	 * int整数转换为4字节的byte数组
	 *
	 * @param i 整数
	 * @return byte数组
	 */
	public static byte[] intToByte4(int i) {
		byte[] targets = new byte[4];
		targets[3] = (byte) (i & 0xFF);
		targets[2] = (byte) (i >> 8 & 0xFF);
		targets[1] = (byte) (i >> 16 & 0xFF);
		targets[0] = (byte) (i >> 24 & 0xFF);
		return targets;
	}

	/**
	 * int整数转换为2字节的byte数组
	 *
	 * @param i 整数
	 * @return byte数组
	 */
	public static byte[] intToByte2(int i) {
		byte[] targets = new byte[2];
		targets[1] = (byte) (i & 0xFF);
		targets[0] = (byte) (i >> 8 & 0xFF);
		return targets;
	}

	/**
	 * byte数组转换为int整数
	 *
	 * @param bytes byte数组
	 * @param off   开始位置
	 * @return int整数
	 */
	public static int byte4ToInt(byte[] bytes, int off) {
		int b0 = bytes[off] & 0xFF;
		int b1 = bytes[off + 1] & 0xFF;
		int b2 = bytes[off + 2] & 0xFF;
		int b3 = bytes[off + 3] & 0xFF;
		return (b0 << 24) | (b1 << 16) | (b2 << 8) | b3;
	}

	/**
	 * byte数组转换为int整数
	 *
	 * @param bytes byte数组
	 * @param off   开始位置
	 * @return int整数
	 */
	public static int byte2ToInt(byte[] bytes, int off) {
		//        String hexStr = conver2HexStr(bytes);
		int b0 = bytes[off] & 0xFF;
		int b1 = bytes[off + 1] & 0xFF;
		int result = (b0 << 8) | b1;
		return result;
	}

	/**
	 * byte数组转换为二进制字符串,每个字节以","隔开
	 */
	public static String conver2HexStr(byte[] b) {
		StringBuffer result = new StringBuffer();
		for (int i = 0; i < b.length; i++) {
			result.append(Long.toString(b[i] & 0xff, 2));
		}
		return result.toString().substring(0, result.length() - 1);
	}

	/**
	 * 以小端模式将int转成byte[]
	 *
	 * @param value
	 * @return
	 */
	public static byte[] int4byteLittle(int value) {
		byte[] src = new byte[4];
		src[3] = (byte) ((value >> 24) & 0xFF);
		src[2] = (byte) ((value >> 16) & 0xFF);
		src[1] = (byte) ((value >> 8) & 0xFF);
		src[0] = (byte) (value & 0xFF);
		return src;
	}

	public static byte[] int2byteLittle(int value) {
		byte[] src = int4byteLittle(value);
		src[3] = (byte) ((value >> 24) & 0xFF);
		src[2] = (byte) ((value >> 16) & 0xFF);
		src[1] = (byte) ((value >> 8) & 0xFF);
		src[0] = (byte) (value & 0xFF);
		return new byte[]{src[1], src[0]};
	}

	/**
	 * 以大端模式将byte[]转成int
	 */
	public static int bytesToIntBig(byte[] src, int offset) {
		int value;
		value =
			(int)
				(((src[offset] & 0xFF) << 24)
					| ((src[offset + 1] & 0xFF) << 16)
					| ((src[offset + 2] & 0xFF) << 8)
					| (src[offset + 3] & 0xFF));
		return value;
	}

	/**
	 * 以小端模式将byte[]转成int
	 */
	public static int bytesToIntLittle(byte[] src, int offset) {
		int value;
		value =
			(int)
				((src[offset] & 0xFF)
					| ((src[offset + 1] & 0xFF) << 8)
					| ((src[offset + 2] & 0xFF) << 16)
					| ((src[offset + 3] & 0xFF) << 24));
		return value;
	}

	public static int byte2int1(byte[] runtimeStatusByte) {
		byte[] targetBytes = new byte[]{0x00, 0x00, 0x00, runtimeStatusByte[0]};
		int b0 = targetBytes[0] & 0xFF;
		int b1 = targetBytes[1] & 0xFF;
		int b2 = targetBytes[2] & 0xFF;
		int b3 = targetBytes[3] & 0xFF;
		return (b0 << 24) | (b1 << 16) | (b2 << 8) | b3;
	}

	public static int byte2int2(byte[] runtimeWpIndexByte) {
		//        byte[] targetBytes = new
		// byte[]{0x00,0x00,runtimeWpIndexByte[0],runtimeWpIndexByte[1]};
		//        int b0 = targetBytes[0] & 0xFF;
		//        int b1 = targetBytes[1] & 0xFF;
		//        int b2 = targetBytes[2] & 0xFF;
		//        int b3 = targetBytes[3] & 0xFF;
		//        return (b0 << 24) | (b1 << 16) | (b2 << 8) | b3;
		byte[] targetBytes = new byte[]{runtimeWpIndexByte[0], runtimeWpIndexByte[1]};
		int b0 = targetBytes[0] & 0xFF;
		int b1 = targetBytes[1] & 0xFF;
		return (b0 << 8) | b1;
	}

	public static int byte2int2LSB(byte[] runtimeWpIndexByte) {
		byte[] targetBytes = new byte[]{runtimeWpIndexByte[1], runtimeWpIndexByte[0], 0x00, 0x00};
		int b0 = targetBytes[0] & 0xFF;
		int b1 = targetBytes[1] & 0xFF;
		int b2 = targetBytes[2] & 0xFF;
		int b3 = targetBytes[3] & 0xFF;
		return (b3 << 24) | (b2 << 16) | (b1 << 8) | b0;
	}

	public static int bytes2Int2LSB(byte[] b) {
		int sum = 0;
		int len = 2;

		for (int i = 1; i > -1; i--) {
			int n = ((int) b[i]) & 0xff;
			n <<= (--len) * 8;
			sum += n;
		}
		return sum;
	}

	// 任意长度
	public static int toInt(byte[] bRefArr) {
		int iOutcome = 0;
		byte bLoop;
		for (int i = 0; i < bRefArr.length; i++) {
			bLoop = bRefArr[i];
			iOutcome += (bLoop & 0xFF) << (8 * i);
		}
		return iOutcome;
	}

	// 任意长度
	public static int toIntLSB(byte[] bRefArr) {
		int iOutcome = 0;
		byte bLoop;
		for (int i = 0; i < bRefArr.length; i++) {
			bLoop = bRefArr[i];
			iOutcome += (bLoop & 0xFF) << (8 * i);
		}
		return iOutcome;
	}

	/**
	 * 以大端模式将int转成byte[]
	 */
	public static byte[] int4BytesBig(int value) {
		byte[] src = new byte[4];
		src[0] = (byte) ((value >> 24) & 0xFF);
		src[1] = (byte) ((value >> 16) & 0xFF);
		src[2] = (byte) ((value >> 8) & 0xFF);
		src[3] = (byte) (value & 0xFF);
		return src;
	}

	public static String byte2hex(byte[] bytes) {
		if (bytes == null || bytes.length == 0) {
			return "";
		}
		char[] buffer = new char[bytes.length * 3];
		int index = 0;
		for (byte b : bytes) {
			buffer[index++] = HEX[b >>> 4 & 0xf];
			buffer[index++] = HEX[b & 0xf];
			buffer[index++] = ' ';
		}
		return new String(buffer, 0, bytes.length * 3 - 1);
	}

	public static String byte2hex(byte var0) {
		String var1 = "";
		String var3 = Integer.toHexString(var0 & 255);
		if (var3.length() == 1) {
			var3 = "0" + var3;
		}
		var1 = var1 + " " + var3;
		return var1;
	}

	public static double[] listTodouble(List<Double> list) {
		Double[] doubles = new Double[list.size()];
		list.toArray(doubles);
		if (doubles == null) {
			return null;
		}
		double[] result = new double[doubles.length];
		for (int i = 0; i < doubles.length; i++) {
			result[i] = doubles[i].doubleValue();
		}
		return result;
	}

	/**
	 * 计算偏移精度(均值与标准差求和)
	 *
	 * @param OffsetSet
	 * @return
	 */
	public static double calculationPrecision(double[] OffsetSet) {
		double sum = 0;
		for (int i = 0; i < OffsetSet.length; i++) {
			sum += OffsetSet[i];
			// 数组的总和
		}
		double average = sum / OffsetSet.length;
		int total = 0;
		for (int i = 0; i < OffsetSet.length; i++) {
			total += (OffsetSet[i] - average) * (OffsetSet[i] - average);
			// 方差
		}
		double standardDeviation = Math.sqrt(total / (OffsetSet.length - 1));
		// 标准差
		return average + standardDeviation;
	}

	/**
	 * 求校验和的算法
	 *
	 * @param b 需要求校验和的字节数组
	 * @return 校验和
	 */
	public static byte sumCheck(byte[] b, int len) {
		int sum = 0;
		for (int i = 0; i < len; i++) {
			sum = sum + b[i];
		}
		return (byte) (sum & 0xff);
	}

	/**
	 * byte[]转bit，以String形式输出；
	 *
	 * @param by
	 * @return
	 */
	public static String getBit(byte[] by) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < by.length; i++) {
			sb.append((by[i] >> 7) & 0x1)
				.append((by[i] >> 6) & 0x1)
				.append((by[i] >> 5) & 0x1)
				.append((by[i] >> 4) & 0x1)
				.append((by[i] >> 3) & 0x1)
				.append((by[i] >> 2) & 0x1)
				.append((by[i] >> 1) & 0x1)
				.append((by[i] >> 0) & 0x1);
		}
		return sb.toString();
	}

	public static double getDouble2(double data) {
		try {
			DecimalFormat df = new DecimalFormat("#.00");
			return Double.parseDouble(df.format(data));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return data;
	}

	public static float getFloat2(float data, String pattern) {
		try {
			DecimalFormat df = new DecimalFormat(pattern);
			return Float.parseFloat(df.format(data));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return data;
	}

	public static String floatToStr(float data, String pattern) {
		try {
			DecimalFormat df = new DecimalFormat(pattern);
			df.setRoundingMode(RoundingMode.HALF_UP);
			return df.format(data);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "";
	}

	/**
	 * 将bytes数组转化为16进制字符串
	 *
	 * @param bArray 数据
	 * @param length 转换的长度
	 */
	public static String bytesToHexString(byte[] bArray, int length) {
		if (bArray == null) {
			return "";
		}
		if (length > bArray.length) {
			length = bArray.length;
		}
		StringBuilder sb = new StringBuilder(length);
		String sTemp;
		for (int i = 0; i < length; i++) {
			sTemp = Integer.toHexString(0xFF & bArray[i]);
			if (sTemp.length() < 2) {
				sb.append(0);
			}
			sb.append(sTemp.toUpperCase());
		}
		return sb.toString();
	}

	/**
	 * 生成两字节校验和，低字节在前，高字节在后（小端模式）。
	 *
	 * @param data data
	 * @return 校验和
	 */
	public static byte[] checksum(byte[] data) {
		int sum = 0;
		for (byte b : data) {
			sum += (b & 0xff); // 将字节转换为无符号整数再相加
		}
		int low = sum & 0xff; // 取低8位
		int high = (sum >> 8) & 0xff; // 取高8位
		return new byte[]{(byte) low, (byte) high}; // 返回校验和的低字节和高字节
	}

	/**
	 * 永茂STT135塔机数据crc校验
	 *
	 * @param buf data
	 * @param len len
	 * @return crc
	 */
	public static byte[] yongMaoCrc16(byte[] buf, int len) {
		int crc = 0xFFFF;
		for (int i = 0; i < len; i++) {
			crc ^= (buf[i] & 0xFF);
			for (int j = 0; j < 8; j++) {
				if ((crc & 0x01) != 0) {
					crc = (crc >> 1) ^ 0xA001;
				} else {
					crc = crc >> 1;
				}
			}
		}
		return new byte[]{(byte) (crc & 0xFF), (byte) ((crc >> 8) & 0xFF)};
	}

	/**
	 * 激光雷达crc16校验，使用CRC-16-CCITT标准
	 *
	 * @param data data
	 * @return crc16
	 */
	public static byte[] fjLidarCrc16(byte[] data) {
		// CRC16-CCITT polynomial: x^16 + x^12 + x^5 + 1 (0x1021)
		int crc = 0xFFFF; // Initial value

		for (byte b : data) {
			crc ^= (b & 0xFF) << 8;
			for (int i = 0; i < 8; i++) {
				if ((crc & 0x8000) != 0) {
					crc = (crc << 1) ^ 0x1021;
				} else {
					crc = crc << 1;
				}
			}
		}
		crc = crc & 0xFFFF;

		// Return 2 bytes in big-endian order (high byte first)
		return new byte[]{
			(byte) ((crc >> 8) & 0xFF),  // high byte
			(byte) (crc & 0xFF)          // low byte
		};
	}

	/**
	 * Converts a hex string (generated by {@link #byte2hex(byte[])}) back to a byte array.
	 * The hex string is expected to be in the format "XX YY ZZ..." (e.g., "0a 1f 2c")
	 * or "XX" for a single byte (e.g., "0a").
	 *
	 * @param hexString The hex string to convert. Must not be null.
	 * @return The corresponding byte array. Returns an empty byte array if the input string is empty.
	 * @throws IllegalArgumentException if the hexString is null, malformed (invalid characters,
	 *                                  incorrect spacing, or incorrect length).
	 */
	public static byte[] hex2Bytes(String hexString) {
		if (hexString == null) {
			throw new IllegalArgumentException("Input hex string cannot be null.");
		}
		if (hexString.isEmpty()) {
			return new byte[0];
		}

		// Validate overall string structure based on length.
		// For n bytes, string is "h1h2 s h3h4 s ... s h(2n-1)h(2n)". Length is 3n-1 for n > 0.
		// So, (length + 1) must be a multiple of 3. numBytes = (length + 1) / 3.
		// Example: "ab" (1 byte) -> len=2. (2+1)/3=1. (2+1)%3=0. Correct.
		// Example: "ab cd" (2 bytes) -> len=5. (5+1)/3=2. (5+1)%3=0. Correct.
		if ((hexString.length() + 1) % 3 != 0) {
			throw new IllegalArgumentException(
				"Invalid hex string length: " + hexString.length() +
					". Expected length (3*n - 1) for n bytes (e.g., 'ab' or 'ab cd'). Input: \"" + hexString + "\"");
		}

		int numBytes = (hexString.length() + 1) / 3;
		byte[] bytes = new byte[numBytes];

		for (int i = 0; i < numBytes; i++) {
			int firstCharIdx = i * 3;
			char c1 = hexString.charAt(firstCharIdx);
			char c2 = hexString.charAt(firstCharIdx + 1); // Safe due to overall length check

			int highNibble = Character.digit(c1, 16);
			int lowNibble = Character.digit(c2, 16);

			if (highNibble == -1 || lowNibble == -1) {
				throw new IllegalArgumentException(
					"Invalid hex character(s) encountered: '" + c1 + c2 +
						"' at index " + firstCharIdx + " in string: \"" + hexString + "\"");
			}
			bytes[i] = (byte) ((highNibble << 4) | lowNibble);

			// Validate space separator if not the last byte pair
			if (i < numBytes - 1) { // If there are more bytes to process
				int spaceIdx = firstCharIdx + 2;
				if (hexString.charAt(spaceIdx) != ' ') {
					throw new IllegalArgumentException(
						"Invalid hex string format: Expected space separator at index " + spaceIdx +
							" in string: \"" + hexString + "\"");
				}
			}
		}
		return bytes;
	}
}
