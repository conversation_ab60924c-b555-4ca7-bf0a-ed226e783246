package com.fj.towercontrol.ui;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.ClickUtils;
import com.blankj.utilcode.util.StringUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.fj.fjprotocol.ntrip.NtripManager;
import com.fj.fjprotocol.ntrip.bean.AddressInfo;
import com.fj.fjprotocol.ntrip.bean.NtripSource;
import com.fj.towercontrol.BuildConfig;
import com.fj.towercontrol.R;
import com.fj.towercontrol.TowerApp;
import com.fj.towercontrol.consts.CraneType;
import com.fj.towercontrol.data.entity.ScreenLog;
import com.fj.towercontrol.data.entity.TowerConfig;
import com.fj.towercontrol.data.entity.VoiceAlarmType;
import com.fj.towercontrol.databinding.ActivityMainBinding;
import com.fj.towercontrol.event.MessageEvent;
import com.fj.towercontrol.event.MqttConnectStatusChangedEvent;
import com.fj.towercontrol.modbus.VoiceModbusMaster;
import com.fj.towercontrol.ui.adapter.ScreenLogAdapter;
import com.fj.towercontrol.ui.base.BaseBindingActivity;
import com.fj.towercontrol.util.MemoryStore;
import com.fj.towercontrol.util.NetworkUtil;
import com.fj.towercontrol.widget.LiftHeightConfigDialog;
import com.fj.towercontrol.widget.NtripEditDialog;
import com.fj.towercontrol.widget.dalog.SystemConfigCallback;
import com.fj.towercontrol.widget.dalog.SystemConfigDialogFragment;
import com.fjdynamics.app.logger.Logger;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.LinkedList;
import java.util.List;
import java.util.Locale;

/**
 * 主界面
 *
 * <AUTHOR>
 */
public class MainActivity extends BaseBindingActivity<ActivityMainBinding> {

	private static final String TAG = "MainActivity";
	private MainViewModel viewModel;
	private ScreenLogAdapter screenLogAdapter;
	private LinearLayoutManager layoutManager;

	@SuppressLint("SetTextI18n")
	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		Logger.d(TAG, "onCreate");
		// TODO: 2023/7/6 界面元素过多导致setContentView耗时1s以上，考虑优化
		viewModel = new ViewModelProvider(this).get(MainViewModel.class);
		initViews();
		viewModel.getEcuVersion().observe(this, ecuVersion -> binding.tvEcuVersion.setText(ecuVersion));
		viewModel.getEcuRealtimeData().observe(this, data -> {
			if (data == null) {
				binding.ecuLayout.tvEcuRealtime.setText("");
				return;
			}
			binding.ecuLayout.tvEcuRealtime.setText(StringUtils.format(
				"""
					父模式：%s，子模式：%s，塔吊当前状态：%s，
					错误码高位：%s，错误码低位：%s，
					遥控使能：%s，吊钩挡位：%s，小车挡位：%s，回转挡位：%s，
					塔吊自身状态高位：%s，自身状态低位：%s，
					塔吊当前错误状态：%s，回转制动迫力灯：%s
					风向：%s，风速：%s，温度：%s，湿度：%s，两分钟平均风速：%s，
					工作半径：%s，塔身倾斜度：%s，吊钩离地高度：%s，塔吊高度：%s，
					当前负载限制：%s，当前半径限制：%s，塔吊类型：%s，
					动臂俯仰角：%s，
					滚转角：%s，俯仰角：%s，塔身航向：%s，吊钩航向：%s，
					吊钩锤面距离：%s，吊钩投影距离：%s，吊绳长度：%s，
					地面驾舱指令：%s，
					ecu到塔机指令：%s，
					吊塔自身状态：on：%s，
					is_luffing_up_work：%s，is_luffing_down_work：%s，
					is_hoist_up_work：%s，is_hoist_down_work：%s，
					控制信号检测：%s，按键：%s，
					塔机当前控制模式：%s，塔机远控时心跳计数：%s，
					塔机工作半径：%s，吊物重量：%s，吊钩离地高度：%s，
					塔吊启动信号：%s，大臂倾角：%s，
					吊钩滚筒震动：ax=%s，ay=%s，az=%s，
					大臂滚筒震动：ax=%s，ay=%s，az=%s，
					塔上钥匙状态：%s，是否触发档位限制：%s，力矩百分比：%s，
					限位1：%s，限位2：%s，预警：%s，
					起升故障：%s，回转故障：%s，变幅故障：%s，
					急停：%s，ecu复位原因：%s，计数：%s，
					塔机CAN数据接收状态：%s，
					ecu->塔机时间：%s，座舱->塔机时间：%s，限控状态：%s, 蜗速状态: %s"""
				,
				data.getModeParent(), data.getModeChild(), data.getCraneStatus(),
				data.getErrorHigh(), data.getErrorLow(),
				data.getRemoteFunction(), data.getHookGear(), data.getTrolleyGear(), data.getSlewGear(),
				data.getCraneSelfStatusHigh(), data.getCraneSelfStatusLow(),
				data.getTowerCraneErrorStatus(), data.getLightSignal(),
				data.getWindDirection(), data.getWindSpeed(), data.getTemperature(), data.getHumidity(), data.getAverageWindSpeed(),
				data.getWorkRadius(), data.getTowerInclination(), data.getHookHeightAboveGround(), data.getTowerCranHeight(),
				data.getLoadingLimit(), data.getRadiusLimit(), data.getTowerCraneType(),
				data.getLuffingAngle(),
				data.getRoll(), data.getPitch(), data.getYaw(), data.getHookYaw(),
				data.getHook1(), data.getHook2(), data.getRopeLength(),
				data.getInstruction(),
				data.getEcuCommand(),
				data.getCraneSelfStatusLow().contains(1) ? 1 : 0,
				data.getCraneSelfStatusHigh().contains(2) ? 1 : 0, data.getCraneSelfStatusHigh().contains(3) ? 1 : 0,
				data.getCraneSelfStatusLow().contains(5) ? 1 : 0, data.getCraneSelfStatusLow().contains(6) ? 1 : 0,
				data.getControlStatus(), data.getButtonStatus(),
				data.getWorkMode(), data.getSanyHeartbeat(),
				data.getSanyWorkRadius(), data.getSanyLiftWeight(), data.getSanyHookHeightAboveGround(),
				data.getPowerOn(), data.getBoomInclination(),
				data.getHookDrumVibration().getAx(), data.getHookDrumVibration().getAy(), data.getHookDrumVibration().getAz(),
				data.getBoomDrumVibration().getAx(), data.getBoomDrumVibration().getAy(), data.getBoomDrumVibration().getAz(),
				data.getLockStatus(), data.getGearLimit(), data.getTorquePercentage(),
				data.getLimit1(), data.getLimit2(),
				data.getWarn(),
				data.getLiftError(), data.getRotateError(), data.getAmplitudeError(),
				data.getEmergencyStop(), data.getEcuRestartReason(), data.getEcuRestartCount(),
				data.getCanStatus(),
				data.getTimeout1(), data.getTimeout2(), data.getLimitControl(), data.getWormSpeed()
			));
		});
		viewModel.getEcuLog().observe(this, log -> binding.ecuLayout.tvEcuLog.setText(log == null ? "" : log.toString()));
		viewModel.getScreenLog().observe(this, this::appendLog);
		viewModel.getCargoWeight().observe(this, cargoWeight -> {
			TowerConfig towerConfig = MemoryStore.getInstance().getTowerConfig();
			double hookPeeledWeight = 0;
			if (towerConfig != null
				&& towerConfig.getCalibration() != null) {
				hookPeeledWeight = towerConfig.getCalibration().getHookPeeledWeight();
			}
			binding.ecuLayout.tvCargoData.setText(StringUtils.format(
				"智能吊钩毛重：%s kg，皮重：%s kg，净重：%s kg",
				cargoWeight == null ? "NA" : cargoWeight.getGrossWeight(),
				hookPeeledWeight,
				cargoWeight == null ? "NA" : cargoWeight.getNetWeight()
			));
		});
		viewModel.getRadarInfo().observe(this, radarInfo -> {
			if (radarInfo == null) {
				binding.ecuLayout.tvRadar.setText("四周最近目标: ");
				return;
			}
			binding.ecuLayout.tvRadar.setText(StringUtils.format("四周最近目标：%.2fm, 角度：%s°, 速度：%.1f m/s, 雷达编号：%s-%s", radarInfo.getDistance(), radarInfo.getDegree(), radarInfo.getSpeed(), radarInfo.getRadarId(), radarInfo.getTargetId()));
		});
		viewModel.getBottomRadarInfo().observe(this, radarInfo -> {
			if (radarInfo == null) {
				binding.ecuLayout.tvRadarBottom.setText("底部最近目标: ");
				return;
			}
			binding.ecuLayout.tvRadarBottom.setText(StringUtils.format("底部最近目标：%.2fm, 角度：%s°, 速度：%.1f m/s, 雷达编号：%s-%s", radarInfo.getDistance(), radarInfo.getDegree(), radarInfo.getSpeed(), radarInfo.getRadarId(), radarInfo.getTargetId()));
		});
		viewModel.getBatteryInfo().observe(this, batteryInfo -> {
			if (batteryInfo == null) {
				binding.ecuLayout.tvBatteryInfo.setText("");
				return;
			}
			binding.ecuLayout.tvBatteryInfo.setText(StringUtils.format("吊钩电池:%s%%,状态:%s", batteryInfo.getPercent(), batteryInfo.getStatus()));
		});
//		viewModel.getTowerCraneData().observe(this, craneData -> {
//			if (craneData == null) {
//				binding.ecuLayout.tvTower.setText("塔机：NA");
//				return;
//			}
//			binding.ecuLayout.tvTower.setText(StringUtils.format(
//				"塔机：当前重量: %s, 吊钩高度: %s, 工作半径: %s",
//				craneData.getCargoWeight(), craneData.getHookHeight(), craneData.getWorkRadius()
//			));
//		});
		viewModel.getRadarDebugInfo().observe(this, radarDebugInfo -> {
			if (TextUtils.isEmpty(radarDebugInfo)) {
				binding.ecuLayout.tvRadarDebug.setText("毫米波雷达：");
				return;
			}
			binding.ecuLayout.tvRadarDebug.setText("毫米波雷达：\n" + radarDebugInfo);
		});

		viewModel.getLidarData().observe(this, lidarData -> {
			if (lidarData == null) {
				return;
			}
			binding.ecuLayout.tvLidar.setText(StringUtils.format(
				"""
					激光雷达：
					SYSTEM: %s, STEADY: %s,
					X-Y平面内圈半径: %s, 外圈半径: %s,
					Z轴探测向上距离: %s, 向下距离: %s,
					最近障碍物距离: %s""",
				lidarData.getTimestamp1(), lidarData.getTimestamp2(),
				lidarData.getMinRadius(), lidarData.getMaxRadius(),
				lidarData.getMinUp(), lidarData.getMinDown(),
				lidarData.getMinDistance()
			));
		});

		// 初始化电台
		//        initRadio();
		initNtrip();
		EventBus.getDefault().register(this);
	}

	private void initViews() {
		binding.tvAppVersion.setText(String.format("APP版本：%s_%s", BuildConfig.VERSION_NAME, BuildConfig.FLAVOR.toUpperCase()));
		binding.tvSn.setText(String.format("设备SN：%s", TowerApp.VEHICLE_SN));

		binding.rvLog.setHasFixedSize(true);
		layoutManager = new LinearLayoutManager(this);
		binding.rvLog.setLayoutManager(layoutManager);
		screenLogAdapter = new ScreenLogAdapter();
		binding.rvLog.setAdapter(screenLogAdapter);

		ClickUtils.applySingleDebouncing(binding.btnConfig, 5_000L, v ->
			SystemConfigDialogFragment.Companion.show(getSupportFragmentManager(), new SystemConfigCallback() {
				@Override
				public void onNtripConfig() {
					if (!NetworkUtil.isNetworkAvailable(MainActivity.this)) {
						Logger.w(TAG, "网络不可用，无法使用ntrip");
						ToastUtils.showShort(R.string.upgrade_net_error);
						return;
					}
					NtripEditDialog ntripEditDialog = new NtripEditDialog(MainActivity.this);
					ntripEditDialog.setCancelable(false);
					ntripEditDialog.setOnDismissListener(dialog -> {
						// 设置隐藏导航栏
						View decorView = getWindow().getDecorView();
						int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN;
						decorView.setSystemUiVisibility(uiOptions);
					});
					ntripEditDialog.show();
				}

				@Override
				public void onCheckUpdate() {
					viewModel.startCheckUpgrade();
				}

				@Override
				public void onDebug(@NonNull CraneType craneType) {
					Intent intent = new Intent(MainActivity.this, CraneDebugActivity.class);
					intent.putExtra(CraneDebugActivity.KEY_CRANE_TYPE, craneType);
					startActivity(intent);
				}

				@Override
				public void onDeviceRegister() {
					viewModel.registerDevice();
				}
			}));

		binding.ecuLayout.btnPlayVoice1.setOnClickListener(v -> {
			VoiceModbusMaster.getInstance().playVoice(VoiceAlarmType.LIFTING_ALARM);
		});
		binding.ecuLayout.btnPlayVoice2.setOnClickListener(v -> {
			VoiceModbusMaster.getInstance().playVoice(VoiceAlarmType.HANGING_ALARM);
		});
//		binding.ecuLayout.btnIo1.setOnClickListener(v -> {
//			AlarmIOModbusReader.getInstance().controlIO(true, false);
//		});
//		binding.ecuLayout.btnIo2.setOnClickListener(v -> {
//			AlarmIOModbusReader.getInstance().controlIO(false, true);
//		});
		binding.ecuLayout.btnAutoLiftConfig.setOnClickListener(v -> {
			LiftHeightConfigDialog dialog = new LiftHeightConfigDialog();
			dialog.show(getSupportFragmentManager(), LiftHeightConfigDialog.TAG);
		});
	}

	private void initNtrip() {
		// 添加ntrip连接状态监听
		NtripManager.getInstance().addNtripListener(new NtripManager.NtripListener() {
			@Override
			public void onGetSource(List<NtripSource> ntripSources) {
			}

			@Override
			public void onStateChange(NtripManager.NtripState ntripState) {
				Logger.d(TAG, "ntrip state changed: " + ntripState.ordinal());
				if (ntripState == NtripManager.NtripState.LINK_TO_NODE_SUCCESS) {
					// 显示当前连接的Ip和端口
					AddressInfo addressInfo = NtripManager.getInstance().getAddressInfo();
					if (addressInfo != null) {
						runOnUiThread(() -> binding.tvNtripStatus.setText(String.format(Locale.getDefault(), "%s:%d", addressInfo.getIp(), addressInfo.getPort())));
					} else {
						runOnUiThread(() -> binding.tvNtripStatus.setText("ntrip未连接"));
						Logger.e(TAG, "Ntrip连接的地址信息为空");
					}
				} else if (ntripState == NtripManager.NtripState.LINKING_TO_NODE) {
					runOnUiThread(() -> binding.tvNtripStatus.setText("ntrip连接中"));
				} else {
					runOnUiThread(() -> binding.tvNtripStatus.setText("ntrip未连接"));
				}
			}
		});
	}

	@Subscribe(threadMode = ThreadMode.MAIN)
	public void onMessageEvent(MessageEvent event) {
		if (event == null || event.getCode() == null) {
			Logger.e(TAG, "onMessageEvent: event or code is null");
			return;
		}
		viewModel.handleMessageEvent(event);
	}

	@Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
	public void onMqttConnectStatusChanged(MqttConnectStatusChangedEvent event) {
		viewModel.handleMqttStatusChanged(event.isConnected());
	}

	@Override
	protected void onDestroy() {
		Logger.d(TAG, "onDestroy");
		EventBus.getDefault().unregister(this);
		super.onDestroy();
	}

	@Override
	public void onBackPressed() {
		// 禁用返回键
	}

	private void appendLog(LinkedList<ScreenLog> log) {
		screenLogAdapter.setData(log);
		layoutManager.scrollToPosition(log.size() - 1);
	}
}
