package com.fj.towercontrol.mqtt.entity;

import com.fj.fjprotocol.data.GeoData;
import com.fj.towercontrol.consts.CraneType;
import com.fj.towercontrol.data.entity.AbsoluteEncoderData;
import com.fj.towercontrol.data.entity.BatteryInfo;
import com.fj.towercontrol.data.entity.CargoWeightInfo;
import com.fj.towercontrol.data.entity.EcuLog;
import com.fj.towercontrol.data.entity.EcuRealtimeData;
import com.fj.towercontrol.data.entity.ImuCalibration;
import com.fj.towercontrol.data.entity.Position;
import com.fj.towercontrol.data.entity.TowerBaseConfig;
import com.fj.towercontrol.data.entity.TowerConfig;
import com.fj.towercontrol.data.entity.TowerCraneData;
import com.fj.towercontrol.data.entity.WeatherStationData;
import com.fj.towercontrol.util.Calculator;
import com.fj.towercontrol.util.EulerAngle2QuatUtil;
import com.fj.towercontrol.util.ExtionsionsKt;
import com.fj.towercontrol.util.GeoUtils;
import com.fj.towercontrol.util.MemoryStore;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

/**
 * 塔吊数据类
 *
 * <AUTHOR>
 */
public class TowerData {
	private String type = "metric";
	/**
	 * utc时间戳
	 */
	private long timestamp;
	/**
	 * 工作状态 1：待机 2：空闲 3：工作 4：锁定
	 */
	private String workStatus;
	/**
	 * 当前工作模式 1：驾舱手动控制模式 2：远程手动控制模式 3：远程自动控制模式
	 */
	private String workMode;
	/**
	 * 风向
	 */
	private double windDirection;
	/**
	 * 风速
	 */
	private double windSpeed;
	/**
	 * 温度
	 */
	private double temperature;
	/**
	 * 湿度
	 */
	private double humidity;
	/**
	 * 吊钩三维地理数据
	 */
	@SerializedName("cliver")
	private Position hangingHook;
	/**
	 * 塔顶三维地理数据
	 */
	private Position towerTop;
	/**
	 * 小车绞盘振动
	 */
//	private Vibration trolleyVibration;
	/**
	 * 吊钩绞盘振动
	 */
//	private Vibration hookVibration;
	/**
	 * 塔身倾斜度
	 */
	private double towerGradient;
	/**
	 * 2分钟平均风速
	 */
	@SerializedName("2minWindSpeed")
	private double averageWindSpeed;
	/**
	 * 吊钩距地面的高度
	 */
	private double heightAboveGround;
	/**
	 * 当前限重(kg)
	 */
	private double weightLimit;
	/**
	 * 当前负载下半径限制
	 */
	private double radiusLimit;
	/**
	 * 工作半径
	 */
	private Double workRadius;
	/**
	 * 塔身高度
	 */
	private double towerHeight;
	/**
	 * 吊钩下降高度
	 */
	private Double descentHeight;
	/**
	 * 遥控-吊钩挡位,-4~0~4,负值下降
	 */
	@SerializedName("cliverGear")
	private int remoteHookGear;
	/**
	 * 遥控-小车挡位,-4~0~4,负值后退
	 */
	@SerializedName("trolleyGear")
	private int remoteTrolleyGear;
	/**
	 * 遥控-回转挡位,-2~0~2,负值向左
	 */
	@SerializedName("gyrationGear")
	private int remoteSpinGear;
	/**
	 * 前臂仰角
	 */
	@SerializedName("forearmElevation")
	private double armDegree;
	/**
	 * ecu上报的遥控-使能按钮状态
	 */
	private List<Integer> remoteStatus;
	/**
	 * 告警雷达编号
	 */
	private Integer alarmRadarIndex;
	/**
	 * 雷达告警角度
	 */
	private Integer alarmRadarAngle;
	/**
	 * 雷达告警距离
	 */
	private Double alarmRadarDistance;
	/**
	 * 电池剩余电量百分比
	 */
	private Double batterySoc;
	/**
	 * 电池状态：0静止，1充电，2放电
	 */
	private Integer batteryStatus;
	/**
	 * 吊物重量
	 */
	private Integer liftWeight;
	/**
	 * 智能吊钩读取到的吊物重量
	 */
	private Integer craneLiftWeight;
	/**
	 * ecu上报的工作半径
	 */
	private double craneWorkRadius;
	/**
	 * 计算出来的吊钩下降高度
	 */
	private double craneDescentHeight;
	/**
	 * 倾角方向
	 */
	private double inclinationDirection;
	/**
	 * 放迫力灯信号<br>
	 * 0:灯灭<br>
	 * 1:灯亮
	 */
	private int lightSignal;
	/**
	 * 雷达旋转角度
	 */
	private double radarShiftingAngle;
	/**
	 * 塔吊是否启动
	 */
	private int powerOn;
	/**
	 * 故障码
	 */
	private List<Integer> errorCode;
	/**
	 * 起升变频器故障代码
	 */
	private String liftError;
	/**
	 * 回转变频器故障代码
	 */
	private String slewError;
	/**
	 * 变幅变频器故障代码
	 */
	private String amplitudeError;
	/**
	 * 急停信号
	 */
	private int emergencyStop;
	/**
	 * 大臂航向
	 */
	private double towerYaw;
	/**
	 * 登录用户uid
	 */
	private Long uid;
	/**
	 * 吊钩摄像头高程
	 */
	private double cameraHeight;
	/**
	 * 吊钩摄像头离地高度
	 */
	private double cameraHeightAboveGround;
	/**
	 * 距吊钩最近安全帽位置
	 */
	private Position helmetPos;
	/**
	 * 有效安全帽数量
	 */
	private int helmetCount;
	/**
	 * 当前是是否限控
	 */
	private boolean limitControl;
	/**
	 * 当前是否是远控
	 */
	private boolean remote;
	/**
	 * 当前蜗速状态
	 */
	private boolean wormSpeed;
	private Long hookEncoder;
	private Long jibEncoder;

	/**
	 * 创建TowerData
	 *
	 * @param ecuData         ecu状态上报
	 * @param ecuLog          ecu实时日志
	 * @param cargoWeightInfo 智能吊钩称重数据
	 * @return TowerData
	 */
	public static TowerData build(EcuRealtimeData ecuData, EcuLog ecuLog, CargoWeightInfo cargoWeightInfo) {
		TowerData towerData = new TowerData();
		towerData.setTimestamp(System.currentTimeMillis());
		towerData.setUid(MemoryStore.getInstance().getUid());
		towerData.setWorkStatus(String.valueOf(ecuData.getCraneStatus()));
		if (ecuData.getModeChild() == 0xa0) {
			towerData.setWorkMode(String.valueOf(ecuData.getModeChild()));
		} else {
			towerData.setWorkMode(String.valueOf(ecuData.getModeParent()));
		}
		towerData.setEmergencyStop(ecuData.getEmergencyStop());
		towerData.setWindDirection(ecuData.getWindDirection());
		towerData.setWindSpeed(ecuData.getWindSpeed());
		towerData.setTemperature(ecuData.getTemperature());
		towerData.setHumidity(ecuData.getHumidity());
		if (ecuLog != null) {
			GeoData hookPos = ecuLog.getHookLocation();
			if (GeoUtils.isGpsValid(ecuLog.getHookGpsStatus()) && hookPos != null) {
				//安全帽里的高程使用的是大地高度，得把吊钩的海拔高度转换成实际大地高(海拔高+似大地水准面差距-天线相位中心高)
				double altAfterCalibration = hookPos.getAlt() + MemoryStore.getInstance().getHookGeoidalHeight() - 0.1221;
				MemoryStore.getInstance().setUploadHookAltitude(altAfterCalibration);
				hookPos.setAlt(altAfterCalibration);
				towerData.setHangingHook(ExtionsionsKt.toPosition(hookPos));
			}
			GeoData towerTopPos = ecuLog.getTowerLocation();
			if (GeoUtils.isGpsValid(ecuLog.getTowerGpsStatus()) && towerTopPos != null) {
				towerData.setTowerTop(ExtionsionsKt.toPosition(towerTopPos));
			}
		}
//		towerData.setTrolleyVibration(ecuData.getTrolleyHoistVibration());
//		towerData.setHookVibration(ecuData.getHookHoistVibration());
		towerData.setAverageWindSpeed(ecuData.getAverageWindSpeed());
		towerData.setHeightAboveGround(ecuData.getHookHeightAboveGround());
		towerData.setErrorCode(new ArrayList<>(ecuData.getErrorLow()));
		towerData.setWeightLimit(ecuData.getLoadingLimit() * 1000);
		towerData.setRadiusLimit(ecuData.getRadiusLimit());
		towerData.setTowerHeight(ecuData.getTowerCranHeight());
		towerData.setRemoteTrolleyGear(ecuData.getTrolleyGear());
		towerData.setRemoteSpinGear(ecuData.getSlewGear());
		towerData.setRemoteHookGear(ecuData.getHookGear());
		towerData.setArmDegree(ecuData.getLuffingAngle());
		towerData.setRemoteStatus(ecuData.getRemoteFunction());
		towerData.setRadarShiftingAngle(Calculator.calculateRadarRotation(ecuData.getYaw(), ecuData.getHookYaw()));

		TowerConfig towerConfig = MemoryStore.getInstance().getTowerConfig();

		//计算塔身倾斜度和倾斜方向
		ImuCalibration imuCalibration = null;
		if (towerConfig != null
			&& towerConfig.getTowerBaseConfig() != null) {
			imuCalibration = towerConfig.getTowerBaseConfig().getImuCalibration();
		}
		double[] imu = EulerAngle2QuatUtil.calcVecCog(ecuData.getPitch(), ecuData.getRoll() + (imuCalibration == null ? 0 : imuCalibration.getRollAngle()));
		double towerGradient = imu[0];
		if (Double.isNaN(towerGradient)) {
			towerGradient = 0;
		}
		towerData.setTowerGradient(towerGradient);
		double inclinationDirection = imu[1];
		if (Double.isNaN(inclinationDirection)) {
			inclinationDirection = 0;
		}
		towerData.setInclinationDirection(inclinationDirection);
		towerData.setLightSignal(ecuData.getLightSignal());
		towerData.setPowerOn(ecuData.getPowerOn());

		double armLength = 0;
		if (towerConfig != null
			&& towerConfig.getTowerBaseConfig() != null) {
			armLength = towerConfig.getTowerBaseConfig().getTowerArmLength();
		}
		double calculatedDescentHeight = Calculator.calculateDescentHeight(
			ecuData.getLuffingAngle(),
			armLength,
			ecuLog == null ? 0 : ecuLog.getTowerLocation().getAlt(),
			0,
			ecuLog == null ? 0 : ecuLog.getHookLocation().getAlt()
		);
		towerData.setCraneDescentHeight((double) Math.round(calculatedDescentHeight * 100) / 100);
		towerData.setCraneWorkRadius(ecuData.getWorkRadius());
		towerData.setCraneLiftWeight(cargoWeightInfo == null ? null : cargoWeightInfo.getNetWeight());

		//离地高度取值优先级: 永茂塔机 > 三一塔机
		double maxHeightAboveGround = 76.4;
		if (towerConfig != null
			&& towerConfig.getTowerBaseConfig() != null
			&& towerConfig.getTowerBaseConfig().getHookConfig() != null) {
			maxHeightAboveGround = towerConfig.getTowerBaseConfig().getHookConfig().getMaxHeightAboveGround();
		}
		towerData.setDescentHeight(maxHeightAboveGround - ecuData.getSanyHookHeightAboveGround());
		towerData.setWorkRadius(ecuData.getSanyWorkRadius());
		towerData.setHeightAboveGround(ecuData.getSanyHookHeightAboveGround());
		//皮重
		int peeledWeight = 0;
		if (towerConfig != null
			&& towerConfig.getCalibration() != null) {
			peeledWeight = (int) towerConfig.getCalibration().getPeeledWeight();
		}
		//去皮后重量
		int liftWeight = (int) (ecuData.getSanyLiftWeight() - peeledWeight);
		if (MemoryStore.getInstance().getSystemConfig().getCraneType() == CraneType.XCMG.ordinal()) {
			towerData.setLiftWeight(cargoWeightInfo == null ? null : cargoWeightInfo.getNetWeight());
		} else {
			towerData.setLiftWeight(liftWeight);
		}

		//如果app能读到气象站数据，使用app读取到的数据
		WeatherStationData weatherData = MemoryStore.getInstance().getWeatherStationData();
		if (weatherData != null) {
			towerData.setTemperature(weatherData.getTemperature());
			towerData.setHumidity(weatherData.getHumidity());
			towerData.setWindDirection(weatherData.getWindDirection());
			towerData.setWindSpeed(weatherData.getWindSpeed() * 3.6);
			towerData.setAverageWindSpeed(weatherData.getWindSpeedAverage() * 3.6);
		}

		//cic塔机数据
		TowerCraneData towerCraneData = MemoryStore.getInstance().getTowerCraneData();
		if (towerCraneData != null) {
			towerData.setWorkRadius(towerCraneData.getWorkRadius());
			towerData.setDescentHeight(towerCraneData.getHookHeight());
			towerData.setLiftWeight(Math.max(towerCraneData.getCargoWeight() - peeledWeight, 0));
		}

		//吊钩电池数据
		BatteryInfo batteryInfo = MemoryStore.getInstance().getBatteryInfo();
		if (batteryInfo != null) {
			towerData.setBatterySoc(batteryInfo.getPercent());
			towerData.setBatteryStatus(batteryInfo.getStatus());
		}

		//变频器故障代码
		towerData.setLiftError(convertInverterErrorCode(ecuData.getLiftError()));
		towerData.setSlewError(convertInverterErrorCode(ecuData.getRotateError()));
		towerData.setAmplitudeError(convertInverterErrorCode(ecuData.getAmplitudeError()));

		//大臂航向
		double towerYaw = ((ecuData.getYaw() + 360) % 360 + 180) % 360;
		towerData.setTowerYaw(towerYaw > 180 ? towerYaw - 360 : towerYaw);

		//计算吊钩摄像头高程与离地高度
		Double[] cameraHeights = calcCameraHeight(ecuData, ecuLog, towerConfig);
		if (cameraHeights.length >= 2) {
			if (cameraHeights[0] != null) {
				towerData.setCameraHeight(cameraHeights[0]);
			}
			if (cameraHeights[1] != null) {
				towerData.setCameraHeightAboveGround(cameraHeights[1]);
			}
		}

		//最近安全帽位置
//		HelmetDTO nearestHelmet = HelmetMqClient.INSTANCE.getNearestHelmet();
//		if (nearestHelmet != null) {
//			towerData.setHelmetPos(new Position(nearestHelmet.getLat(), nearestHelmet.getLng(), nearestHelmet.getAlt()));
//		}
//		towerData.setHelmetCount(HelmetMqClient.INSTANCE.getHelmetCount());

		towerData.setLimitControl(ecuData.getLimitControl() > 0);
		towerData.setRemote(ecuData.getWorkMode() != 0);
		towerData.setWormSpeed(ecuData.getWormSpeed() != 0);
		AbsoluteEncoderData encoderData = MemoryStore.getInstance().getAbsoluteEncoderData();
		if (encoderData != null) {
			towerData.setHookEncoder(encoderData.getHookValue());
			towerData.setJibEncoder(encoderData.getJibValue());
		}
		return towerData;
	}

	private static Double[] calcCameraHeight(EcuRealtimeData ecuData, EcuLog ecuLog, TowerConfig towerConfig) {
		Double[] result = new Double[2];
		if (ecuData == null
			|| towerConfig == null
			|| towerConfig.getTowerBaseConfig() == null) {
			return result;
		}

		TowerBaseConfig towerBaseConfig = towerConfig.getTowerBaseConfig();
		Double jibLength = towerBaseConfig.getTowerArmLength();
		if (jibLength == null) {
			return result;
		}

		GeoData towerTopPos = towerBaseConfig.getTowerTopPos();
		Double towerTopHeight = null;
		if (ecuLog != null
			&& GeoUtils.isGpsValid(ecuLog.getTowerGpsStatus())) {
			towerTopHeight = ecuLog.getTowerLocation().getAlt() + MemoryStore.getInstance().getHookGeoidalHeight();
		} else if (towerTopPos != null) {
			towerTopHeight = towerTopPos.getAlt();
		}
		if (towerTopHeight == null) {
			return result;
		}
		//大臂前端距塔顶高度
		double diffHeight = jibLength * Math.sin(Math.toRadians(ecuData.getLuffingAngle()));

		//摄像头高程
		result[0] = towerTopHeight + diffHeight;
		GeoData towerBasePos = towerBaseConfig.getTowerPos();
		if (towerBasePos != null) {
			//摄像头离地高度
			result[1] = result[0] - towerBasePos.getAlt();
		}
		return result;
	}

	private static String convertInverterErrorCode(int rawErrorCode) {
		if (rawErrorCode == 0) {
			return null;
		}
		return "E" + rawErrorCode;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getWorkStatus() {
		return workStatus;
	}

	public void setWorkStatus(String workStatus) {
		this.workStatus = workStatus;
	}

	public String getWorkMode() {
		return workMode;
	}

	public void setWorkMode(String workMode) {
		this.workMode = workMode;
	}

	public double getWindDirection() {
		return windDirection;
	}

	public void setWindDirection(double windDirection) {
		this.windDirection = windDirection;
	}

	public double getWindSpeed() {
		return windSpeed;
	}

	public void setWindSpeed(double windSpeed) {
		this.windSpeed = windSpeed;
	}

	public double getTemperature() {
		return temperature;
	}

	public void setTemperature(double temperature) {
		this.temperature = temperature;
	}

	public double getHumidity() {
		return humidity;
	}

	public void setHumidity(double humidity) {
		this.humidity = humidity;
	}

	public Position getHangingHook() {
		return hangingHook;
	}

	public void setHangingHook(Position hangingHook) {
		this.hangingHook = hangingHook;
	}

	public Position getTowerTop() {
		return towerTop;
	}

	public void setTowerTop(Position towerTop) {
		this.towerTop = towerTop;
	}

//	public Vibration getTrolleyVibration() {
//		return trolleyVibration;
//	}
//
//	public void setTrolleyVibration(Vibration trolleyVibration) {
//		this.trolleyVibration = trolleyVibration;
//	}
//
//	public Vibration getHookVibration() {
//		return hookVibration;
//	}
//
//	public void setHookVibration(Vibration hookVibration) {
//		this.hookVibration = hookVibration;
//	}

	public double getTowerGradient() {
		return towerGradient;
	}

	public void setTowerGradient(double towerGradient) {
		this.towerGradient = towerGradient;
	}

	public double getAverageWindSpeed() {
		return averageWindSpeed;
	}

	public void setAverageWindSpeed(double averageWindSpeed) {
		this.averageWindSpeed = averageWindSpeed;
	}

	public double getHeightAboveGround() {
		return heightAboveGround;
	}

	public void setHeightAboveGround(double heightAboveGround) {
		this.heightAboveGround = heightAboveGround;
	}

	public List<Integer> getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(List<Integer> errorCode) {
		this.errorCode = errorCode;
	}

	public double getWeightLimit() {
		return weightLimit;
	}

	public void setWeightLimit(double weightLimit) {
		this.weightLimit = weightLimit;
	}

	public Double getWorkRadius() {
		return workRadius;
	}

	public void setWorkRadius(Double workRadius) {
		this.workRadius = workRadius;
	}

	public double getTowerHeight() {
		return towerHeight;
	}

	public void setTowerHeight(double towerHeight) {
		this.towerHeight = towerHeight;
	}

	public Double getDescentHeight() {
		return descentHeight;
	}

	public void setDescentHeight(Double descentHeight) {
		this.descentHeight = descentHeight;
	}

	public int getRemoteHookGear() {
		return remoteHookGear;
	}

	public void setRemoteHookGear(int remoteHookGear) {
		this.remoteHookGear = remoteHookGear;
	}

	public int getRemoteTrolleyGear() {
		return remoteTrolleyGear;
	}

	public void setRemoteTrolleyGear(int remoteTrolleyGear) {
		this.remoteTrolleyGear = remoteTrolleyGear;
	}

	public int getRemoteSpinGear() {
		return remoteSpinGear;
	}

	public void setRemoteSpinGear(int remoteSpinGear) {
		this.remoteSpinGear = remoteSpinGear;
	}

	public Integer getAlarmRadarIndex() {
		return alarmRadarIndex;
	}

	public void setAlarmRadarIndex(Integer alarmRadarIndex) {
		this.alarmRadarIndex = alarmRadarIndex;
	}

	public Integer getAlarmRadarAngle() {
		return alarmRadarAngle;
	}

	public void setAlarmRadarAngle(Integer alarmRadarAngle) {
		this.alarmRadarAngle = alarmRadarAngle;
	}

	public Double getAlarmRadarDistance() {
		return alarmRadarDistance;
	}

	public void setAlarmRadarDistance(Double alarmRadarDistance) {
		this.alarmRadarDistance = alarmRadarDistance;
	}

	public double getArmDegree() {
		return armDegree;
	}

	public void setArmDegree(double armDegree) {
		this.armDegree = armDegree;
	}

	public Double getBatterySoc() {
		return batterySoc;
	}

	public void setBatterySoc(Double batterySoc) {
		this.batterySoc = batterySoc;
	}

	public Integer getBatteryStatus() {
		return batteryStatus;
	}

	public void setBatteryStatus(Integer batteryStatus) {
		this.batteryStatus = batteryStatus;
	}

	public Integer getLiftWeight() {
		return liftWeight;
	}

	public void setLiftWeight(Integer liftWeight) {
		this.liftWeight = liftWeight;
	}

	public double getRadiusLimit() {
		return radiusLimit;
	}

	public void setRadiusLimit(double radiusLimit) {
		this.radiusLimit = radiusLimit;
	}

	public List<Integer> getRemoteStatus() {
		return remoteStatus;
	}

	public void setRemoteStatus(List<Integer> remoteStatus) {
		this.remoteStatus = remoteStatus;
	}

	public int getLightSignal() {
		return lightSignal;
	}

	public void setLightSignal(int lightSignal) {
		this.lightSignal = lightSignal;
	}

	public double getInclinationDirection() {
		return inclinationDirection;
	}

	public void setInclinationDirection(double inclinationDirection) {
		this.inclinationDirection = inclinationDirection;
	}

	public double getRadarShiftingAngle() {
		return radarShiftingAngle;
	}

	public void setRadarShiftingAngle(double radarShiftingAngle) {
		this.radarShiftingAngle = radarShiftingAngle;
	}

	public Integer getCraneLiftWeight() {
		return craneLiftWeight;
	}

	public void setCraneLiftWeight(Integer craneLiftWeight) {
		this.craneLiftWeight = craneLiftWeight;
	}

	public double getCraneWorkRadius() {
		return craneWorkRadius;
	}

	public void setCraneWorkRadius(double craneWorkRadius) {
		this.craneWorkRadius = craneWorkRadius;
	}

	public double getCraneDescentHeight() {
		return craneDescentHeight;
	}

	public void setCraneDescentHeight(double craneDescentHeight) {
		this.craneDescentHeight = craneDescentHeight;
	}

	public int getPowerOn() {
		return powerOn;
	}

	public void setPowerOn(int powerOn) {
		this.powerOn = powerOn;
	}

	public String getLiftError() {
		return liftError;
	}

	public void setLiftError(String liftError) {
		this.liftError = liftError;
	}

	public String getSlewError() {
		return slewError;
	}

	public void setSlewError(String slewError) {
		this.slewError = slewError;
	}

	public String getAmplitudeError() {
		return amplitudeError;
	}

	public void setAmplitudeError(String amplitudeError) {
		this.amplitudeError = amplitudeError;
	}

	public int getEmergencyStop() {
		return emergencyStop;
	}

	public void setEmergencyStop(int emergencyStop) {
		this.emergencyStop = emergencyStop;
	}

	public double getTowerYaw() {
		return towerYaw;
	}

	public void setTowerYaw(double towerYaw) {
		this.towerYaw = towerYaw;
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public double getCameraHeight() {
		return cameraHeight;
	}

	public void setCameraHeight(double cameraHeight) {
		this.cameraHeight = cameraHeight;
	}

	public Position getHelmetPos() {
		return helmetPos;
	}

	public void setHelmetPos(Position helmetPos) {
		this.helmetPos = helmetPos;
	}

	public boolean isLimitControl() {
		return limitControl;
	}

	public void setLimitControl(boolean limitControl) {
		this.limitControl = limitControl;
	}

	public int getHelmetCount() {
		return helmetCount;
	}

	public void setHelmetCount(int helmetCount) {
		this.helmetCount = helmetCount;
	}

	public boolean isRemote() {
		return remote;
	}

	public void setRemote(boolean remote) {
		this.remote = remote;
	}

	public boolean isWormSpeed() {
		return wormSpeed;
	}

	public void setWormSpeed(boolean wormSpeed) {
		this.wormSpeed = wormSpeed;
	}

	public double getCameraHeightAboveGround() {
		return cameraHeightAboveGround;
	}

	public void setCameraHeightAboveGround(double cameraHeightAboveGround) {
		this.cameraHeightAboveGround = cameraHeightAboveGround;
	}

	public Long getHookEncoder() {
		return hookEncoder;
	}

	public void setHookEncoder(Long hookEncoder) {
		this.hookEncoder = hookEncoder;
	}

	public Long getJibEncoder() {
		return jibEncoder;
	}

	public void setJibEncoder(Long jibEncoder) {
		this.jibEncoder = jibEncoder;
	}
}
