package com.fj.towercontrol.data.entity;

import androidx.annotation.NonNull;

import com.fj.fjprotocol.data.GeoData;
import com.fjd.app.common.util.DataUtil;

import java.util.Locale;

/**
 * Ecu日志上报
 *
 * <AUTHOR>
 */
public class EcuLog {
	/**
	 * ECU系统时间戳
	 */
	private final long timestamp;
	/**
	 * 吊钩gps状态
	 * <p>
	 * 1:单点<br>
	 * 4:固定
	 */
	private final int hookGpsStatus;
	/**
	 * 吊钩卫星数量
	 */
	private final int hookSatelliteCount;
	/**
	 * 吊钩位置
	 */
	private final GeoData hookLocation;
	/**
	 * 塔身gps状态
	 * <p>
	 * 1:单点<br>
	 * 4:固定
	 */
	private final int towerGpsStatus;
	/**
	 * 塔身卫星数量
	 */
	private final int towerSatelliteCount;
	/**
	 * 塔身位置
	 */
	private final GeoData towerLocation;
	/**
	 * 旋转中心位置
	 */
	private final GeoData centerLocation;

	public EcuLog(byte[] data) {
		int idx = 0;
		int len = 4;
		timestamp = DataUtil.unsigned4BytesToInt(DataUtil.subBytes(data, idx, len), 0);
		idx += len;
		len = 1;
		hookGpsStatus = data[idx] & 0xff;
		idx += len;
		hookSatelliteCount = data[idx] & 0xff;
		idx += len;
		len = 8;
		double hookLat = DataUtil.bytesToLong(DataUtil.subBytes(data, idx, len)) / Math.pow(10, 10);
		idx += len;
		double hookLng = DataUtil.bytesToLong(DataUtil.subBytes(data, idx, len)) / Math.pow(10, 10);
		idx += len;
		len = 4;
		double hookAlt = DataUtil.convertFourSignInt(DataUtil.subBytes(data, idx, len)) * 1.0 / 10_000;
		idx += len;
		double hookNorth = DataUtil.byte4ToInt(DataUtil.subBytes(data, idx, len), 0) * 1.0 / 10_000;
		idx += len;
		double hookEast = DataUtil.byte4ToInt(DataUtil.subBytes(data, idx, len), 0) * 1.0 / 10_000;
		idx += len;
		//实际大地高=大地高-杆高-天线相位中心高
		hookLocation = new GeoData(hookLat, hookLng, hookAlt, hookNorth, hookEast);
		len = 1;
		towerGpsStatus = data[idx] & 0xff;
		idx += len;
		towerSatelliteCount = data[idx] & 0xff;
		idx += len;
		len = 8;
		double towerLat = DataUtil.bytesToLong(DataUtil.subBytes(data, idx, len)) / Math.pow(10, 10);
		idx += len;
		double towerLng = DataUtil.bytesToLong(DataUtil.subBytes(data, idx, len)) / Math.pow(10, 10);
		idx += len;
		len = 4;
		double towerAlt = DataUtil.convertFourSignInt(DataUtil.subBytes(data, idx, len)) * 1.0 / 10_000;
		idx += len;
		towerLocation = new GeoData(towerLat, towerLng, towerAlt);
		len = 8;
		double latitudeC = DataUtil.bytesToLong(DataUtil.subBytes(data, idx, len)) / Math.pow(10, 10);
		idx += len;
		double longitudeC = DataUtil.bytesToLong(DataUtil.subBytes(data, idx, len)) / Math.pow(10, 10);
		idx += len;
		len = 4;
		double heightC = DataUtil.convertFourSignInt(DataUtil.subBytes(data, idx, len)) * 1.0 / 10_000;
		idx += len;
		centerLocation = new GeoData(latitudeC, longitudeC, heightC);

//		// A点纬度
//		int latALen = 8;
//		byte[] latitudeABytes = DataUtil.subBytes(data, idx, latALen);
//		double latitudeA = DataUtil.bytesToLong(latitudeABytes) / Math.pow(10, 10);
//		idx += latALen;
//		// A点经度
//		int lngALen = 8;
//		byte[] lngABytes = DataUtil.subBytes(data, idx, lngALen);
//		double longitudeA = DataUtil.bytesToLong(lngABytes) / Math.pow(10, 10);
//		idx += lngALen;
//		// A点高度
//		int heightALen = 4;
//		byte[] heightABytes = DataUtil.subBytes(data, idx, heightALen);
//		double heightA = DataUtil.byte4ToInt(heightABytes, 0) * 1.0 / 10_000;
//		idx += heightALen;
//		// B点纬度
//		int latBLen = 8;
//		byte[] latitudeBBytes = DataUtil.subBytes(data, idx, latBLen);
//		double latitudeB = DataUtil.bytesToLong(latitudeBBytes) / Math.pow(10, 10);
//		idx += latBLen;
//		// B点经度
//		int lngBLen = 8;
//		byte[] lngBBytes = DataUtil.subBytes(data, idx, lngBLen);
//		double longitudeB = DataUtil.bytesToLong(lngBBytes) / Math.pow(10, 10);
//		idx += lngBLen;
//		// B点高度
//		int heightBLen = 4;
//		byte[] heightBBytes = DataUtil.subBytes(data, idx, heightBLen);
//		double heightB = DataUtil.byte4ToInt(heightBBytes, 0) * 1.0 / 10_000;
//		idx += heightBLen;
//		// A点北向位置
//		int northALen = 4;
//		byte[] northABytes = DataUtil.subBytes(data, idx, northALen);
//		double northA = DataUtil.byte4ToInt(northABytes, 0) * 1.0 / 10_000;
//		idx += northALen;
//		// A点东向位置
//		int eastALen = 4;
//		byte[] eastABytes = DataUtil.subBytes(data, idx, eastALen);
//		double eastA = DataUtil.byte4ToInt(eastABytes, 0) * 1.0 / 10_000;
//		idx += eastALen;
//		// B点北向位置
//		int northBLen = 4;
//		byte[] northBBytes = DataUtil.subBytes(data, idx, northBLen);
//		double northB = DataUtil.byte4ToInt(northBBytes, 0) * 1.0 / 10_000;
//		idx += northBLen;
//		// B点东向位置
//		int eastBLen = 4;
//		byte[] eastBBytes = DataUtil.subBytes(data, idx, eastBLen);
//		double eastB = DataUtil.byte4ToInt(eastBBytes, 0) * 1.0 / 10_000;
//		idx += eastBLen;
//		// 旋转中心北向位置
//		int northCLen = 4;
//		byte[] northCBytes = DataUtil.subBytes(data, idx, northCLen);
//		double northC = DataUtil.byte4ToInt(northCBytes, 0) * 1.0 / 10_000;
//		idx += northCLen;
//		// 旋转中心东向位置
//		int eastCLen = 4;
//		byte[] eastCBytes = DataUtil.subBytes(data, idx, eastCLen);
//		double eastC = DataUtil.byte4ToInt(eastCBytes, 0) * 1.0 / 10_000;
//		idx += eastCLen;
	}

	public int getHookGpsStatus() {
		return hookGpsStatus;
	}

	public GeoData getHookLocation() {
		return hookLocation;
	}

	public int getTowerGpsStatus() {
		return towerGpsStatus;
	}

	public GeoData getTowerLocation() {
		return towerLocation;
	}

	@NonNull
	@Override
	public String toString() {
		return String.format(Locale.getDefault(),
			"""
				时间戳：%s，
				吊钩gps状态：%s，卫星数量：%s，
				吊钩纬度：%s，经度：%s，高度：%s，
				吊钩北向位置：%s，东向位置：%s，
				塔身gps状态：%s，塔身卫星数量：%s，
				塔身纬度：%s，经度：%s，高度：%s，
				旋转中心纬度：%s，经度：%s，高度：%s""",
			timestamp,
			hookGpsStatus, hookSatelliteCount,
			hookLocation.getLat(), hookLocation.getLng(), hookLocation.getAlt(),
			hookLocation.getNorth(), hookLocation.getEast(),
			towerGpsStatus, towerSatelliteCount,
			towerLocation.getLat(), towerLocation.getLng(), towerLocation.getAlt(),
			centerLocation.getLat(), centerLocation.getLng(), centerLocation.getAlt()
		);
	}
}
