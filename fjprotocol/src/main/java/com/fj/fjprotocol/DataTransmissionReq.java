package com.fj.fjprotocol;

import com.fjd.app.common.util.DataUtil;

public class DataTransmissionReq {

	/**
	 * 0x01 请求航线配置 0x02 请求航点 0x03 请求航线配置 0x04 请求离散操纵指令及数据 0x10 请求版本号 0x21 请求行走系状态 0x22 请求作业系状态 0x23
	 * 请求全状态（行走系+作业系） 0x24 请求障碍物信息
	 */
	private int cmdId;

	public DataTransmissionReq() {
	}

	public int getCmdId() {
		return cmdId;
	}

	public void setCmdId(int cmdId) {
		this.cmdId = cmdId;
	}

	public DataTransmissionReq(int cmdId) {
		this.cmdId = cmdId;
	}

	public byte[] generateBytesData() {
		byte[] cmdIdBytes = DataUtil.int2byte1(cmdId);
		DataRcToAPFormatter dataRcToAPFormatter = new DataRcToAPFormatter();
		dataRcToAPFormatter.setDataLength(DataUtil.int2byte2(cmdIdBytes.length - 1));
		dataRcToAPFormatter.setData(cmdIdBytes);
		return dataRcToAPFormatter.getFormattedData();
	}
}
