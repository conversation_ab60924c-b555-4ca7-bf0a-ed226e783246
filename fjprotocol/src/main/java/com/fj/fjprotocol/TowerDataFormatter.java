package com.fj.fjprotocol;

import static com.fj.fjprotocol.TransManager.sReqValue;

import com.fjd.app.common.util.DataUtil;

import java.util.Arrays;

public class TowerDataFormatter {

	private static final String TAG = "TowerDataFormatter";
	private byte[] head = {(byte) 0xeb, (byte) 0x90}; // head of frame
	private byte[] version = {
		(byte) 0xff, (byte) 0xff, (byte) 0xff, (byte) 0xff
	}; // version no 0xFFFFFFFF
	private byte[] src = {0x00, 0x00, 0x00, 0x00}; // 发送方 0x00000000
	private byte[] target = {0x00, 0x00, 0x00, 0x00}; // 接收方 0x00000001
	private byte[] seq = {0x00, 0x00}; // 初始化随机，单调递增回绕（各自为主体递增）
	private byte[] shouldAck = {0x01}; // 是否需要回应（确认位）, 广播模式不用ACK回复
	private byte[] ackSeq = {0x00, 0x00}; // 确认序列号 ACK_Seq, 回复确认赋值与收到的Seq一致即可
	private byte[] dataLength; // 数据长度, the length of data
	private byte[] data; // 指令 + 指令 数据
	private byte[] checkSum; // 校验和
	private byte[] tail = {0x0d, 0x0a}; // 帧尾

	public byte[] getFormattedData() {
		setSeq();
		byte[] firstData =
			new byte[]{
				head[0],
				head[1],
				version[0],
				version[1],
				version[2],
				version[3],
				src[0],
				src[1],
				src[2],
				src[3],
				target[0],
				target[1],
				target[2],
				target[3],
				seq[0],
				seq[1],
				shouldAck[0],
				ackSeq[0],
				ackSeq[1],
				dataLength[0],
				dataLength[1]
			};
		byte[] secondData = DataUtil.byteMerger(firstData, data);
		byte[] checkSumBytes = DataUtil.subBytes(secondData, 2, secondData.length - 2);
		byte checkSumByte = DataUtil.sumCheck(checkSumBytes, checkSumBytes.length);
		this.checkSum = new byte[]{checkSumByte};
		byte[] thirdData = DataUtil.byteMerger(secondData, checkSum);
		byte[] finalData = DataUtil.byteMerger(thirdData, tail);
		return finalData.clone();
	}

	public byte[] getHead() {
		if (head == null) {
			return null;
		} else {
			return head.clone();
		}
	}

	public void setHead(byte[] head) {
		if (head == null) {
			this.head = null;
		} else {
			this.head = head.clone();
		}
	}

	public byte[] getVersion() {
		if (version == null) {
			return null;
		} else {
			return version.clone();
		}
	}

	public void setVersion(byte[] version) {
		if (version == null) {
			this.version = null;
		} else {
			this.version = version.clone();
		}
	}

	public byte[] getSrc() {
		if (src == null) {
			return null;
		} else {
			return src.clone();
		}
	}

	public void setSrc(byte[] src) {
		if (src == null) {
			this.src = null;
		} else {
			this.src = src.clone();
		}
	}

	public byte[] getTarget() {
		if (target == null) {
			return null;
		}
		return target.clone();
	}

	public void setTarget(byte[] target) {
		if (target == null) {
			this.target = null;
		} else {
			this.target = target.clone();
		}
	}

	public byte[] getSeq() {
		if (seq == null) {
			return null;
		} else {
			return seq.clone();
		}
	}

	public void setSeq() {
		int seqTmp = sReqValue;
		sReqValue++;
		this.seq = DataUtil.int2byte2(seqTmp);
	}

	public byte[] getShouldAck() {
		if (shouldAck == null) {
			return null;
		} else {
			return shouldAck.clone();
		}
	}

	public void setShouldAck(byte[] shouldAck) {
		if (shouldAck == null) {
			this.shouldAck = null;
		} else {
			this.shouldAck = shouldAck.clone();
		}
	}

	public byte[] getAckSeq() {
		if (ackSeq == null) {
			return null;
		} else {
			return ackSeq.clone();
		}
	}

	public void setAckSeq(byte[] ackSeq) {
		this.ackSeq = ackSeq.clone();
	}

	public byte[] getDataLength() {
		if (dataLength == null) {
			return null;
		} else {
			return dataLength.clone();
		}
	}

	public void setDataLength(byte[] dataLength) {
		if (dataLength == null) {
			this.dataLength = null;
		} else {
			this.dataLength = dataLength.clone();
		}
	}

	public byte[] getData() {
		if (data == null) {
			return null;
		} else {
			return data.clone();
		}
	}

	public void setData(byte[] data) {
		if (data == null) {
			this.data = null;
		} else {
			this.data = data.clone();
		}
	}

	public byte[] getCheckSum() {
		if (checkSum == null) {
			return null;
		} else {
			return checkSum.clone();
		}
	}

	public void setCheckSum(byte[] checkSum) {
		if (checkSum == null) {
			this.checkSum = null;
		} else {
			this.checkSum = checkSum.clone();
		}
	}

	public byte[] getTail() {
		if (tail == null) {
			return null;
		} else {
			return tail.clone();
		}
	}

	public void setTail(byte[] tail) {
		if (tail == null) {
			this.tail = null;
		} else {
			this.tail = tail.clone();
		}
	}

	@Override
	public String toString() {
		return "DataRcToAPFormatter{"
			+ "head="
			+ Arrays.toString(head)
			+ ", version="
			+ Arrays.toString(version)
			+ ", src="
			+ Arrays.toString(src)
			+ ", target="
			+ Arrays.toString(target)
			+ ", seq="
			+ Arrays.toString(seq)
			+ ", shouldAck="
			+ shouldAck
			+ ", ackSeq="
			+ Arrays.toString(ackSeq)
			+ ", dataLength="
			+ dataLength
			+ ", data="
			+ Arrays.toString(data)
			+ ", checkSum="
			+ checkSum
			+ ", tail="
			+ Arrays.toString(tail)
			+ '}';
	}
}
