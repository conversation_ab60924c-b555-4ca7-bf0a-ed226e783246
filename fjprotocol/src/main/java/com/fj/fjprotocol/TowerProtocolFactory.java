package com.fj.fjprotocol;

import android.util.Log;

import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.EcuLogger;
import com.fjdynamics.protocollibrary.factory.BaseFactory;

/**
 * 塔吊ecu数据接收包封装工厂类
 *
 * <AUTHOR>
 */
public class TowerProtocolFactory extends BaseFactory<TowerProtocol> {

	private String TAG;
	private static final int CHECK_DATA_LENGTH = 1024;
	byte[] buffer = new byte[0];

	private final EcuDataCallback ecuDataCallback;

	private long lastTimestamp;

	public TowerProtocolFactory(EcuDataCallback ecuDataCallback, String tag) {
		setHeader(TowerProtocol.header);
		setFooter(TowerProtocol.footer);
		setCmdSize(1);
		setCmdDataSize(2);
		setCmdDataSizeIndex(19);
		this.ecuDataCallback = ecuDataCallback;
		TAG = tag;
	}

	@Override
	public void HandleData(byte[] data) {
		if (null == data || data.length == 0) {
			return;
		}
		buffer = appendBuffer(data);
		int startIndex = findHead(buffer);
		while (startIndex >= 0 && buffer.length >= startIndex + 24) {
			// 判断数据长度 ecu数据最低为24位
			// 有完整数据
			int dataLength = DataUtil.bytes2Int(buffer[startIndex + 19], buffer[startIndex + 20]);
			// 当某一帧数据出错后，dataLength可能会很大,
			if (dataLength > CHECK_DATA_LENGTH) {
				buffer = new byte[0];
				break;
			}
			if (buffer.length <= startIndex + 24 + dataLength) {
				break;
			}
			if (buffer[startIndex] == getHeader()[0] && buffer[startIndex + 1] == getHeader()[1]) {
				// 头正确
				int tailIndex = startIndex + dataLength + 23;
				if (buffer[tailIndex] == getFooter()[0]
					&& buffer[tailIndex + 1] == getFooter()[1]) {
					// 尾正确
					byte[] usefulBytes = new byte[tailIndex + 2 - startIndex];
					System.arraycopy(buffer, startIndex, usefulBytes, 0, usefulBytes.length);
					this.GotCmdData(usefulBytes);

					buffer =
						DataUtil.subBytes(
							buffer,
							tailIndex + 2 - startIndex,
							buffer.length - tailIndex - 2 + startIndex);
					startIndex = findHead(buffer);
				} else {
					// 尾不对,舍弃头
					buffer =
						DataUtil.subBytes(
							buffer,
							tailIndex + 2 - startIndex,
							buffer.length - tailIndex - 2 + startIndex);
					startIndex = findHead(buffer);
				}
			}
		}
		if (startIndex < 0 && buffer.length >= 2) {
			buffer = new byte[0];
		}
	}

	@Override
	public void GotCmdData(byte[] bytes) {
		// 对数据进行校验
		byte[] checkBytes = DataUtil.subBytes(bytes, 2, bytes.length - 5);
		byte checkSum = DataUtil.sumCheck(checkBytes, checkBytes.length);
		if (checkSum != bytes[bytes.length - 3]) {
			Log.w(TAG, "TowerProtocol recv ecu data sum check failed");
			return;
		}
		if (bytes[21] != (byte) 0xa2) {
			//按键上报频率太快，只打印非按键上报的数据
			Log.d(TAG, "TowerProtocol recv ecu data: " + DataUtil.byte2hex(bytes));
		}
		TowerProtocol protocol = new TowerProtocol();
		protocol.setDataSize(DataUtil.byte2ToInt(new byte[]{bytes[19], bytes[20]}, 0));
		protocol.setCmd(bytes[21]);
		byte[] data = new byte[protocol.getDataSize()];
		System.arraycopy(bytes, 22, data, 0, protocol.getDataSize());
		protocol.setData(data);
		protocol.setNeedSaveLog((bytes[16] & 0x01) == 1);
		if (protocol.isNeedSaveLog()) {
			EcuLogger.d(TAG, DataUtil.byte2hex(bytes));
		}
//		if (protocol.isHighFreq()) {
//			long currentTimestamp = System.currentTimeMillis();
//			if (currentTimestamp - lastTimestamp < 995) {
//				return;
//			}
//			lastTimestamp = currentTimestamp;
//		}
		this.BroadData(protocol);
	}

	@Override
	public void BroadData(TowerProtocol towerProtocol) {
		if (ecuDataCallback != null) {
			ecuDataCallback.onTowerDataReceived(towerProtocol);
		}
	}

	private int findHead(byte[] data) {
		for (int i = 0; i < data.length - 1; ++i) {
			if (data[i] == getHeader()[0] && data[i + 1] == getHeader()[1]) {
				return i;
			}
		}
		return -1;
	}

	private int findTail(byte[] data) {
		for (int i = 0; i < data.length - 1; ++i) {
			if ((data[i] & getFooter()[0]) == 13 && (data[i + 1] & 255) == getFooter()[1]) {
				return i;
			}
		}
		return -1;
	}

	private byte[] appendBuffer(byte[] data) {
		byte[] result = new byte[buffer.length + data.length];
		System.arraycopy(buffer, 0, result, 0, buffer.length);
		System.arraycopy(data, 0, result, buffer.length, data.length);
		return result;
	}
}
