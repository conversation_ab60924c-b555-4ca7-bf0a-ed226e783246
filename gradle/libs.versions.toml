[versions]
activity = "1.10.1"
agp = "8.12.0"
core-ktx = "1.16.0"
fj-coordsystem = "1.2.8-20230609.054244-2"
fragment = "1.8.8"
kotlin = "2.2.0"
marineapi = "0.12.0"
okhttp-bom = "4.12.0"
retrofit = "3.0.0"
junit = "1.2.1"

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
sentry = { id = "io.sentry.android.gradle", version = "5.8.0" }

[libraries]
androidx-activity = { module = "androidx.activity:activity", version.ref = "activity" }
androidx-activity-ktx = { module = "androidx.activity:activity-ktx", version.ref = "activity" }
androidx-appcompat = "androidx.appcompat:appcompat:1.7.1"
androidx-constraintlayout = "androidx.constraintlayout:constraintlayout:2.2.1"
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "core-ktx" }
androidx-fragment = { module = "androidx.fragment:fragment", version.ref = "fragment" }
androidx-fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "fragment" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junit" }
androidx-test-espresso = "androidx.test.espresso:espresso-core:3.6.1"
bcpkix-jdk15on = "org.bouncycastle:bcpkix-jdk15on:1.59"
desugar_jdk = "com.android.tools:desugar_jdk_libs:2.1.5"
eventbus = "org.greenrobot:eventbus:3.3.1"
fj-coordsystem = { module = "com.fjdynamics.app:FJCoordSystem", version.ref = "fj-coordsystem" }
fjdynamics-math = "com.fjdynamics.algorithmgroup:math:1.0.2"
fjdynamics-protocollibrary = "com.fjdynamics.app:protocollibrary:2.0.2.5"
fjdynamics-tractorprotocol = "com.fjdynamics.app:tractorprotocol:1.0.77-SNAPSHOT"
google-material = "com.google.android.material:material:1.12.0"
gson = "com.google.code.gson:gson:2.13.1"
hivemq = "com.hivemq:hivemq-mqtt-client:1.3.7"
java-websocket = "org.java-websocket:Java-WebSocket:1.6.0"
joda-time = "net.danlew:android.joda:2.13.1"
junit = "junit:junit:4.13.2"
marineapi = { module = "net.sf.marineapi:marineapi", version.ref = "marineapi" }
mmkv = "com.tencent:mmkv:1.3.14"
okhttp-bom = { module = "com.squareup.okhttp3:okhttp-bom", version.ref = "okhttp-bom" }
okhttp-logging-interceptor = { module = "com.squareup.okhttp3:logging-interceptor" }
okhttp-okhttp = { module = "com.squareup.okhttp3:okhttp" }
opencsv = "com.opencsv:opencsv:5.11.2"
retrofit-gsonConverter = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit" }
retrofit-retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
retrofit-urlManager = "com.github.JessYanCoding:RetrofitUrlManager:v1.4.0"
timber = "com.jakewharton.timber:timber:4.7.1"
utilcode = "com.blankj:utilcodex:1.31.1"

[bundles]
okhttp = ["okhttp-okhttp", "okhttp-logging-interceptor"]
retrofit = ["retrofit-retrofit", "retrofit-gsonConverter", "retrofit-urlManager"]
